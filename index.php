<?php
	ob_start();
	$pageTitle = 'الرئيسية - Falak';
	include 'init.php';
?>

<div class="container">
    <div class="main-content">
        <!-- Main Content Area -->
        <div class="content-area">
            <!-- Featured Downloads Section -->
            <section class="featured-section">
                <h2 class="section-title">البرامج المميزة</h2>
                <div class="products-grid">
                    <?php
                        $featuredItems = getAllFrom("*", "programs", "", "", "Pro_ID", "DESC");
                        $count = 0;
                        foreach ($featuredItems as $item) {
                            if ($count >= 6) break; // عرض 6 برامج فقط في القسم المميز
                            echo '<div class="product-card">';
                                echo '<img class="product-image" src="uploads/images/' . $item['Image'] .'" alt="' . $item['Name'] . '" />';
                                echo '<div class="product-info">';
                                    echo '<h3 class="product-title">';
                                        echo '<a href="items.php?itemid='. $item['Pro_ID'] .'">' . $item['Name'] . '</a>';
                                    echo '</h3>';
                                    echo '<p class="product-description">' . $item['Short_Des'] . '</p>';
                                    echo '<div class="product-meta">';
                                        echo '<span class="download-count">' . $item['Count_Download'] . ' تحميل</span>';
                                        echo '<div class="product-rating">';
                                            // عرض 5 نجوم (يمكن تحسينها لاحقاً بنظام تقييم حقيقي)
                                            for ($i = 0; $i < 5; $i++) {
                                                echo '<span class="star">★</span>';
                                            }
                                        echo '</div>';
                                    echo '</div>';
                                    echo '<a class="btn-download" href="items.php?itemid='. $item['Pro_ID'] .'">تحميل</a>';
                                echo '</div>';
                            echo '</div>';
                            $count++;
                        }
                    ?>
                </div>
            </section>

            <!-- All Programs Section -->
            <section>
                <h2 class="section-title">جميع البرامج</h2>
                <div class="products-grid">
                    <?php
                        $allItems = getAllFrom("*", "programs", "", "", "Pro_ID", "DESC");
                        foreach ($allItems as $item) {
                            echo '<div class="product-card">';
                                echo '<img class="product-image" src="uploads/images/' . $item['Image'] .'" alt="' . $item['Name'] . '" />';
                                echo '<div class="product-info">';
                                    echo '<h3 class="product-title">';
                                        echo '<a href="items.php?itemid='. $item['Pro_ID'] .'">' . $item['Name'] . '</a>';
                                    echo '</h3>';
                                    echo '<p class="product-description">' . $item['Short_Des'] . '</p>';
                                    echo '<div class="product-meta">';
                                        echo '<span class="download-count">' . $item['Count_Download'] . ' تحميل</span>';
                                        echo '<div class="product-rating">';
                                            for ($i = 0; $i < 5; $i++) {
                                                echo '<span class="star">★</span>';
                                            }
                                        echo '</div>';
                                    echo '</div>';
                                    echo '<a class="btn-download" href="items.php?itemid='. $item['Pro_ID'] .'">تحميل</a>';
                                echo '</div>';
                            echo '</div>';
                        }
                    ?>
                </div>
            </section>
        </div>

        <!-- Sidebar -->
        <aside class="sidebar">
            <!-- Search Widget -->
            <div class="sidebar-widget">
                <div class="widget-header">البحث</div>
                <div class="widget-content">
                    <form class="search-form" method="get" action="search.php">
                        <input type="search" class="search-field" placeholder="ابحث عن البرامج..." name="s" />
                        <button type="submit" class="search-btn">بحث</button>
                    </form>
                </div>
            </div>

            <!-- Categories Widget -->
            <div class="sidebar-widget">
                <div class="widget-header">الفئات</div>
                <div class="widget-content">
                    <ul>
                        <?php
                            $allCats = getAllFrom("*", "categories", "", "", "Cat_ID", "ASC");
                            foreach ($allCats as $cat) {
                                echo '<li><a href="categories.php?pageid=' . $cat['Cat_ID'] . '">' . $cat['Cat_Name'] . '</a></li>';
                            }
                        ?>
                    </ul>
                </div>
            </div>

            <!-- Top Downloads Widget -->
            <div class="sidebar-widget">
                <div class="widget-header">الأكثر تحميلاً</div>
                <div class="widget-content">
                    <ul>
                        <?php
                            $topItems = getTopform("*", "programs", "Count_Download");
                            foreach ($topItems as $item) {
                                echo '<li>';
                                    echo '<a href="items.php?itemid=' . $item['Pro_ID'] . '">' . $item['Name'] . '</a>';
                                    echo '<small class="text-muted d-block">' . $item['Count_Download'] . ' تحميل</small>';
                                echo '</li>';
                            }
                        ?>
                    </ul>
                </div>
            </div>

            <!-- Statistics Widget -->
            <div class="sidebar-widget">
                <div class="widget-header">إحصائيات الموقع</div>
                <div class="widget-content">
                    <ul>
                        <?php
                            $totalPrograms = countItems("Pro_ID", "programs");
                            $totalCategories = countItems("Cat_ID", "categories");
                            echo '<li>عدد البرامج: ' . $totalPrograms . '</li>';
                            echo '<li>عدد الفئات: ' . $totalCategories . '</li>';
                        ?>
                    </ul>
                </div>
            </div>
        </aside>
    </div>
</div>

<?php
	include $tpl . 'footer.php';
	ob_end_flush();
?>