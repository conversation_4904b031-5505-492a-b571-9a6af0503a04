<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8" />
		<title><?php getTitle() ?></title>
		<link rel="stylesheet" href="<?php echo $css ?>bootstrap.min.css" />
		<link rel="stylesheet" href="<?php echo $css ?>font-awesome.min.css" />
		<link rel="stylesheet" href="<?php echo $css ?>jquery-ui.css" />
		<link rel="stylesheet" href="<?php echo $css ?>jquery.selectBoxIt.css" />
		<link rel="stylesheet" href="<?php echo $css ?>front.css" />
	</head>
	<body>
        <!-- Site Header -->
        <header class="site-header">
            <div class="container">
                <div class="header-content">
                    <div class="logo-section">
                        <a href="index.php" class="logo">Falak</a>
                        <div class="tagline">أفضل البرامج المختارة بعناية</div>
                    </div>
                    <div class="header-actions">
                        <!-- يمكن إضافة عناصر إضافية هنا مثل البحث أو تسجيل الدخول -->
                    </div>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="navbar">
            <div class="container">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">الرئيسية</a>
                    </li>
                    <?php
                        $allCats = getAllFrom("*", "categories", "", "", "Cat_ID", "ASC");
                        foreach ($allCats as $cat) {
                            echo '<li class="nav-item">
                                    <a class="nav-link" href="categories.php?pageid=' . $cat['Cat_ID'] . '">
                                        ' . $cat['Cat_Name'] . '
                                    </a>
                                  </li>';
                        }
                    ?>
                </ul>
            </div>
        </nav>

        
  