/* DownloadCrew Style CSS */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

h1, h2, h3, h4, h5, h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 15px;
}

h1 {
    font-size: 28px;
}

h2 {
    font-size: 24px;
}

h3 {
    font-size: 20px;
}

a {
    color: #007bff;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #0056b3;
    text-decoration: none;
}

/* Header Styles */
.site-header {
    background: #fff;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 32px;
    font-weight: 700;
    color: #007bff;
    text-decoration: none;
}

.logo:hover {
    color: #0056b3;
}

.tagline {
    font-size: 14px;
    color: #6c757d;
    margin-top: 5px;
}

/* Navigation Styles */
.navbar {
    background: #343a40;
    padding: 0;
    border: none;
    border-radius: 0;
    margin-bottom: 0;
}

.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0;
}

.nav-link {
    display: block;
    padding: 15px 20px;
    color: #fff !important;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.nav-link:hover {
    background-color: #495057;
    color: #fff !important;
}

.nav-link.active {
    background-color: #007bff;
}

/* Main Content Layout */
.main-content {
    display: flex;
    gap: 30px;
    margin-top: 30px;
}

.content-area {
    flex: 1;
}

.sidebar {
    width: 280px;
    flex-shrink: 0;
}

/* Product Cards */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.product-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: box-shadow 0.3s ease, transform 0.3s ease;
    position: relative;
}

.product-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.product-image {
    width: 100%;
    height: 220px;
    object-fit: cover;
    border-bottom: 1px solid #e9ecef;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-info {
    padding: 20px;
}

.product-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
    line-height: 1.4;
}

.product-title a {
    color: inherit;
    text-decoration: none;
}

.product-title a:hover {
    color: #007bff;
}

.product-description {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.download-count {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    position: relative;
    overflow: hidden;
}

.download-count::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.product-card:hover .download-count::before {
    left: 100%;
}

.product-rating {
    display: flex;
    gap: 2px;
}

.star {
    color: #ffc107;
    font-size: 14px;
}

.btn-download {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    width: 100%;
}

.btn-download:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
}

/* Sidebar Styles */
.sidebar-widget {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 25px;
    overflow: hidden;
}

.widget-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    color: #2c3e50;
}

.widget-content {
    padding: 20px;
}

.widget-content ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.widget-content li {
    margin-bottom: 8px;
}

.widget-content li a {
    color: #495057;
    font-size: 14px;
    padding: 5px 0;
    display: block;
    transition: color 0.3s ease;
}

.widget-content li a:hover {
    color: #007bff;
    padding-left: 10px;
}

.widget-content li small {
    font-size: 12px;
    color: #6c757d;
    margin-top: 2px;
}

.d-block {
    display: block;
}

/* Search Widget */
.search-widget .search-form {
    display: flex;
    gap: 10px;
}

.search-field {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-size: 14px;
}

.search-field:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.search-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
}

.search-btn:hover {
    background: #0056b3;
}

.input-container {
	position: relative;
}

.asterisk {
    font-size: 20px;
    position: absolute;
    right: 10px;
    top: 7px;
    color: #D20707;
}

.main-form .asterisk {
    font-size: 30px;
    position: absolute;
    right: 30px;
    top: 8px;
    color: #D20707;
}

.nice-message {
	padding: 10px;
	background-color: #FFF;
	margin: 10px 0;
	border-left: 5px solid #080;
}

/* End Main Rulez */

/* Start Bootstrap Edits */

.navbar {
	border-radius: 0;
	margin-bottom: 0;
	background: rgba(44, 62, 80, 0.95) !important;
	backdrop-filter: blur(10px);
	box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
	border: none;
}

.nav > li > a,
.navbar-brand {
	padding: 15px 20px;
	transition: all 0.3s ease;
}

.navbar-brand {
	font-size: 2.2em;
    color: #ecf0f1 !important;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.navbar-brand:hover {
    color: #3498db !important;
    transform: scale(1.05);
}

.nav-link {
    color: #ecf0f1 !important;
    font-weight: 500;
    position: relative;
}

.nav-link:hover {
    color: #3498db !important;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: #3498db;
    transition: all 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
    left: 0;
}

.navbar-inverse .navbar-nav > .open>a,
.navbar-inverse .navbar-nav > .open>a:focus,
.navbar-inverse .navbar-nav > .open>a:hover,
.dropdown-menu {
    background-color: #3498db;
}

.dropdown-menu {
	min-width: 180px;
	padding: 0;
	font-size: 1em;
	border: none;
	border-radius: 0;
}

.dropdown-menu > li > a {
	color: #FFF;
	padding: 10px 15px;
}

.dropdown-menu > li > a:focus,
.dropdown-menu > li > a:hover {
    color: #FFF;
    background-color: #8e44ad;
}

.form-control {
	position: relative;
}

/* End Bootstrap Edits */

/* Start Header */

.upper-bar {
	padding: 10px;
	background-color: #FFF
}

.image {
	width: 300px;
	height: 300px;
	border-radius: 15px;
	object-fit: cover;
	transition: transform 0.3s ease;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.image:hover {
	transform: scale(1.05);
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* End Header */

/* Featured Section */
.featured-section {
    margin-bottom: 40px;
}

.section-title {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 2px solid #007bff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
        gap: 20px;
    }

    .sidebar {
        width: 100%;
        order: -1;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .navbar-nav {
        flex-direction: column;
        width: 100%;
    }

    .nav-link {
        text-align: center;
        border-bottom: 1px solid #495057;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }

    .product-info {
        padding: 15px;
    }

    .widget-content {
        padding: 15px;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-muted {
    color: #6c757d;
}

.mb-3 {
    margin-bottom: 1rem;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.mt-3 {
    margin-top: 1rem;
}

.mt-4 {
    margin-top: 1.5rem;
}

/* Legacy Support */
.well {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.thumbnail {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.btn-primary {
    background: #007bff;
    border: 1px solid #007bff;
    color: white;
    padding: 8px 16px;
    border-radius: 5px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #0056b3;
    border-color: #0056b3;
    color: white;
    text-decoration: none;
}

/* Start Login Page */

.login-page form,
.the-errors {
	max-width: 380px;
	margin: auto;
}

.login-page form input {
	margin-bottom: 10px;
}

.login-page [data-class="login"].selected {
	color: #337AB7;
}

.login-page [data-class="signup"].selected {
	color: #5cb85c;
}

.login-page h1 {
	color: #C0C0C0;
}

.login-page h1 span {
	cursor: pointer;
}

.login-page .signup {
	display: none;
}

.the-errors .msg {
    padding: 10px;
    text-align: left;
    background-color: #fff;
    margin-bottom: 8px;
    border-right: 1px solid #e0e0e0;
    border-top: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
    color: #919191;
}

.the-errors .error {
    border-left: 5px solid #cd6858
}

/* End Login Page */

/* Start Categories Page */

.item-box {
	position: relative;
	background-color: #FFF;
}

.item-box .price-tag {
    background-color: #4B4B4B;
    padding: 2px 10px;
    position: absolute;
    right: 0;
    top: 10px;
    font-weight: bold;
    color: #FFF;
}

.item-box .approve-status {
    position: absolute;
    top: 40px;
    left: 0;
    background-color: #b85a5a;
    color: #FFF;
    padding: 3px 5px;
}

.item-box .caption p {
	height: 44px;
	max-height: 44px;
}

/* End Categories Page */

/* Start Show Item Page */

.item-info h2 {
	padding: 10px;
	margin: 0;
}

.item-info p {
	padding: 10px;
}

.item-info ul li { 
	padding: 10px;
}

.item-info ul li:nth-child(odd) {
	background-color: #e8e8e8;
}

.item-info ul li span {
	display: inline-block;
	width: 120px;
}

.tags-items a {
    display: inline-block;
    background-color: #e2e2e2;
    padding: 2px 10px;
    border-radius: 5px;
    color: #666;
    margin-right: 5px;
}


.search{
    padding: 4px 4px;
    margin-top: -5px;
}

.widget-title {
  margin-bottom: 10px;
  font-size: 20px;
  line-height: 1.5;
  text-transform: none;
}

.widget ul li {
  list-style-type: none;
  position: inherit;
  margin-bottom: .5em;
}
/* End Show Item Page */

/* Start Our Custom */

.custom-hr {
	border-top: 1px solid #c9c9c9;
}

/* End Our Custom */
a{
    text-decoration: aquamarine;
    color: #252525
}

.color-aside{
	background-color: #f8f8f8;
}

.set_mergin{
    margin-left: 30px;
}